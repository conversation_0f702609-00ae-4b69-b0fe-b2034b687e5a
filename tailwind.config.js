/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    './index.html',
    './frontend/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // DOAXVV-inspired accent colors
        'accent-ocean': "hsl(var(--accent-ocean))",
        'accent-pink': "hsl(var(--accent-pink))",
        'accent-purple': "hsl(var(--accent-purple))",
        'accent-gold': "hsl(var(--accent-gold))",
        'accent-cyan': "hsl(var(--accent-cyan))",
        'accent-coral': "hsl(var(--accent-coral))",
        'accent-green': "134 61% 41%", // For success states
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      backdropBlur: {
        'xs': '2px',
      },
      fontSize: {
        '2xs': '0.625rem',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      screens: {
        'xs': '475px',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'shimmer': 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
  safelist: [
    // DOAXVV accent colors for dynamic usage
    'text-accent-ocean',
    'text-accent-pink', 
    'text-accent-purple',
    'text-accent-gold',
    'text-accent-cyan',
    'text-accent-coral',
    'text-accent-green',
    'bg-accent-ocean',
    'bg-accent-pink',
    'bg-accent-purple', 
    'bg-accent-gold',
    'bg-accent-cyan',
    'bg-accent-coral',
    'bg-accent-green',
    'bg-accent-ocean/10',
    'bg-accent-pink/10',
    'bg-accent-purple/10',
    'bg-accent-gold/10', 
    'bg-accent-cyan/10',
    'bg-accent-coral/10',
    'bg-accent-green/10',
    'bg-accent-ocean/20',
    'bg-accent-pink/20',
    'bg-accent-purple/20',
    'bg-accent-gold/20',
    'bg-accent-cyan/20', 
    'bg-accent-coral/20',
    'bg-accent-green/20',
    'border-accent-ocean',
    'border-accent-pink',
    'border-accent-purple',
    'border-accent-gold',
    'border-accent-cyan',
    'border-accent-coral',
    'border-accent-green',
    'from-accent-ocean',
    'from-accent-pink',
    'from-accent-purple',
    'from-accent-gold',
    'from-accent-cyan', 
    'from-accent-coral',
    'to-accent-ocean',
    'to-accent-pink',
    'to-accent-purple',
    'to-accent-gold',
    'to-accent-cyan',
    'to-accent-coral',
    'hover:bg-accent-ocean/20',
    'hover:bg-accent-pink/20',
    'hover:bg-accent-purple/20',
    'hover:bg-accent-gold/20',
    'hover:bg-accent-cyan/20',
    'hover:bg-accent-coral/20',
    'group-hover:text-accent-ocean',
    'group-hover:text-accent-pink',
    'group-hover:text-accent-purple',
    'group-hover:text-accent-gold',
    'group-hover:text-accent-cyan',
    'group-hover:text-accent-coral',
  ],
} 